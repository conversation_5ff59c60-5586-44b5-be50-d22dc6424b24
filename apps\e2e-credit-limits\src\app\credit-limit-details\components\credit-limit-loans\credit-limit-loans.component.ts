import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { selectDateFormat } from '@tenant-management/lib/state';
import { Observable } from 'rxjs';
import { selectTransformedCreditLimitLoans } from '../../+state';
import { CreditLimitLoan } from '../../models/credit-limit-loan';

@Component({
  selector: 'e2e-credit-limit-loans',
  templateUrl: './credit-limit-loans.component.html',
  styleUrls: ['./credit-limit-loans.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreditLimitLoansComponent implements OnInit {
  creditLimitLoans$ = new Observable<CreditLimitLoan[]>();
  dateFormat$ = new Observable<string>();

  // Define the columns to display in the table
  displayedColumns: string[] = [
    'loanId',
    'loan',
    'product',
    'originalPrincipalAmount',
    'outstandingPrincipalAmount',
    'earlyRepaymentAmount',
    'interestRate',
    'disbursementDate',
    'maturityDate',
    'status',
    'view',
  ];

  constructor(private store: Store) {}

  ngOnInit() {
    this.creditLimitLoans$ = this.store.select(
      selectTransformedCreditLimitLoans
    );
    this.dateFormat$ = this.store.select(selectDateFormat);
  }
}
