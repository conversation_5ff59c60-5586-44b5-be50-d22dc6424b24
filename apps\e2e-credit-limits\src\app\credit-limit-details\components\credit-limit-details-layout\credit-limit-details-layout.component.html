<mat-sidenav-container class="side-nav-container-full">
  <mat-sidenav
    [mode]="rightSidePanelOptions.mode"
    [opened]="isQuickActionsPanelOpen"
    [position]="rightSidePanelOptions.position"
    [fixedInViewport]="rightSidePanelOptions.fixed"
    [fixedTopGap]="rightSidePanelOptions.top"
    [fixedBottomGap]="rightSidePanelOptions.bottom"
    [disableClose]="true"
  >
    <div class="h-full side-nav-wrapper" tabindex="0">
      <div class="flex justify-end items-center">
        <button
          mat-icon-button
          type="button"
          color="primary"
          (click)="navigateToLoanDetails()"
        >
          <mat-icon class="mat-tm-icon mat-tm-icon-size-24">close</mat-icon>
        </button>
      </div>

      <router-outlet
        name="actions-side-panel"
        (activate)="toggleQuickActionsPanel()"
        (deactivate)="toggleQuickActionsPanel()"
      ></router-outlet>
    </div>
  </mat-sidenav>
  <mat-sidenav-content class="px-2">
    <ng-container
      *ngIf="getCreditLimitReference$ | async as creditLimitReference"
    >
      <div class="flex justify-between items-center mb-5 pe-6">
        <div class="flex items-center">
          <div class="me-3.5">
            <tm-back-to-previous-page
              route="operations/credit-limits"
            ></tm-back-to-previous-page>
          </div>
          <div class="title-gradient-primary text-3xl font-bold me-2.5">
            <span class="me-2">
              {{ 'credit-limit-details.title' | transloco }}
            </span>
            <span class="text-[22px]">
              {{ creditLimitReference.referenceName }}
            </span>
          </div>
        </div>

        <div class="flex">
          <div *ngIf="isEarlyRepayment">
            <button
              type="button"
              mat-stroked-button
              color="primary"
              class="rounded-lg h-11"
              routerLink="./"
            >
              <!-- (click)="navigateToPortfolioAndCollateral()" -->
              <mat-icon class="mat-tm-icon mat-tm-icon-size-20 me-2">
                close
              </mat-icon>
              <span class="text-sm">
                {{ 'general.buttons.cancel' | transloco }}
              </span>
            </button>

            <!-- [disabled]="
          hasPartiesField.value !== false && !partyDetails.addedParties.length
        " -->
            <!-- *ngIf="showNavigationButtons"
        (click)="navigateToNextStep()" -->
            <button
              type="button"
              mat-flat-button
              color="primary"
              class="rounded-lg h-11 ms-6"
              routerLink="simulate"
            >
              <div class="flex items-center">
                <span class="text-sm me-2">
                  {{ 'general.buttons.simulate' | transloco }}
                </span>
                <mat-icon class="mat-tm-icon mat-tm-icon-size-20">
                  arrow_forward
                </mat-icon>
              </div>
            </button>
          </div>

          <e2e-print-and-download
            *ngIf="!isEarlyRepayment"
            printTitle="{{ creditLimitReference.referenceName }}"
            [isPrintTitleTranslatable]="false"
          ></e2e-print-and-download>
        </div>
      </div>

      <div *ngIf="isEarlyRepayment">
        <div class="grow pe-4">
          <div class="operation-details-height ps-1 pe-2 pb-4">
            <router-outlet></router-outlet>
          </div>
        </div>
      </div>

      <div id="print-section" *ngIf="!isEarlyRepayment">
        <div class="flex w-full">
          <div class="flex-1 min-w-0 pe-4">
            <div class="operation-details-height ps-1 pe-2 pb-4">
              <e2e-credit-limit-preview></e2e-credit-limit-preview>
            </div>
          </div>

          <ng-container
            *ngIf="getCreditLimitActions$ | async as creditLimitActions"
          >
            <div class="pe-2 min-w-[327px] w-[327px] flex-shrink-0">
              <div class="operation-details-height-secondary-1 pe-2">
                <div class="px-1 mb-6">
                  <e2e-credit-limit-status></e2e-credit-limit-status>
                </div>
              </div>

              <div
                *ngIf="creditLimitActions.hasActions"
                class="text-end pt-5 pb-3 pe-6"
              >
                <ng-container
                  *ngIf="getCreditLimitActionsMenu$ | async as actionMenus"
                >
                  <e2e-actions-menu
                    [actionMenus]="actionMenus.actionMenus"
                    [customActionMenus]="actionMenus.customActionMenus"
                    (customAction)="cancelCreditLimit($event)"
                    [isNamedOutlet]="true"
                  ></e2e-actions-menu>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </mat-sidenav-content>
</mat-sidenav-container>
