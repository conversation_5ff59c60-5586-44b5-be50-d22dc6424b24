<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexible Scrollable Flexbox Layout</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            display: flex;
            width: 100%;
            height: 500px; /* Set a height for demonstration */
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Scrollable table container - takes remaining space */
        .table-container {
            flex: 1; /* Takes all available space */
            min-width: 0; /* Important: allows flex item to shrink below content size */
            overflow-x: auto; /* Horizontal scroll when needed */
            overflow-y: auto; /* Vertical scroll if needed */
            border-right: 2px solid #eee;
        }

        /* Fixed width sidebar */
        .sidebar {
            width: 300px; /* Fixed width */
            flex-shrink: 0; /* Prevents shrinking */
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto; /* Vertical scroll if content is too tall */
        }

        /* Table styling */
        table {
            width: max-content; /* Table grows based on content */
            min-width: 100%; /* Ensures table takes at least full container width */
            border-collapse: collapse;
            background-color: white;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            white-space: nowrap; /* Prevents text wrapping */
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        /* Sidebar content styling */
        .sidebar h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .sidebar p {
            margin-bottom: 10px;
            line-height: 1.5;
            color: #666;
        }

        .sidebar .info-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        /* Demo controls */
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            border: 2px solid #ddd;
        }

        .controls button {
            padding: 8px 16px;
            margin-right: 10px;
            margin-bottom: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .controls button:hover {
            background-color: #0056b3;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }
            
            .sidebar {
                width: 100%;
                order: -1; /* Move sidebar to top on mobile */
            }
            
            .table-container {
                height: 400px; /* Fixed height on mobile */
            }
        }
    </style>
</head>
<body>
    <h1>Flexible Scrollable Flexbox Layout Solution</h1>
    
    <div class="controls">
        <button onclick="addColumn()">Add Column</button>
        <button onclick="removeColumn()">Remove Column</button>
        <button onclick="addRows()">Add 5 Rows</button>
        <button onclick="clearTable()">Reset Table</button>
    </div>

    <div class="container">
        <div class="table-container">
            <table id="dynamicTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Department</th>
                        <th>Position</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>John Doe</td>
                        <td><EMAIL></td>
                        <td>Engineering</td>
                        <td>Senior Developer</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>Jane Smith</td>
                        <td><EMAIL></td>
                        <td>Marketing</td>
                        <td>Marketing Manager</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>Bob Johnson</td>
                        <td><EMAIL></td>
                        <td>Sales</td>
                        <td>Sales Representative</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="sidebar">
            <h3>Fixed Sidebar (300px)</h3>
            <div class="info-box">
                <strong>Key Features:</strong>
                <p>✓ Always visible on screen</p>
                <p>✓ Fixed 300px width</p>
                <p>✓ Won't be pushed off-screen</p>
            </div>
            
            <p>This sidebar maintains its position and visibility regardless of how much the table content grows.</p>
            
            <p>The table container will show horizontal scrollbars when the table content exceeds the available space.</p>
            
            <div class="info-box">
                <strong>Responsive:</strong>
                <p>On mobile devices, the layout switches to vertical stacking for better usability.</p>
            </div>
        </div>
    </div>

    <script>
        let columnCount = 5;
        let rowCount = 3;

        function addColumn() {
            const table = document.getElementById('dynamicTable');
            const headerRow = table.querySelector('thead tr');
            const bodyRows = table.querySelectorAll('tbody tr');
            
            columnCount++;
            
            // Add header
            const th = document.createElement('th');
            th.textContent = `Column ${columnCount}`;
            headerRow.appendChild(th);
            
            // Add cells to existing rows
            bodyRows.forEach((row, index) => {
                const td = document.createElement('td');
                td.textContent = `Data ${columnCount}-${index + 1}`;
                row.appendChild(td);
            });
        }

        function removeColumn() {
            if (columnCount <= 2) return; // Keep at least 2 columns
            
            const table = document.getElementById('dynamicTable');
            const headerRow = table.querySelector('thead tr');
            const bodyRows = table.querySelectorAll('tbody tr');
            
            // Remove last header cell
            headerRow.removeChild(headerRow.lastElementChild);
            
            // Remove last cell from each row
            bodyRows.forEach(row => {
                row.removeChild(row.lastElementChild);
            });
            
            columnCount--;
        }

        function addRows() {
            const tbody = document.querySelector('#dynamicTable tbody');
            
            for (let i = 0; i < 5; i++) {
                rowCount++;
                const row = document.createElement('tr');
                
                for (let j = 1; j <= columnCount; j++) {
                    const td = document.createElement('td');
                    if (j === 1) {
                        td.textContent = rowCount;
                    } else if (j === 2) {
                        td.textContent = `User ${rowCount}`;
                    } else if (j === 3) {
                        td.textContent = `user${rowCount}@example.com`;
                    } else {
                        td.textContent = `Data ${j}-${rowCount}`;
                    }
                    row.appendChild(td);
                }
                
                tbody.appendChild(row);
            }
        }

        function clearTable() {
            const tbody = document.querySelector('#dynamicTable tbody');
            tbody.innerHTML = `
                <tr>
                    <td>1</td>
                    <td>John Doe</td>
                    <td><EMAIL></td>
                    <td>Engineering</td>
                    <td>Senior Developer</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Jane Smith</td>
                    <td><EMAIL></td>
                    <td>Marketing</td>
                    <td>Marketing Manager</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Bob Johnson</td>
                    <td><EMAIL></td>
                    <td>Sales</td>
                    <td>Sales Representative</td>
                </tr>
            `;
            
            // Reset table headers
            const headerRow = document.querySelector('#dynamicTable thead tr');
            headerRow.innerHTML = `
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Department</th>
                <th>Position</th>
            `;
            
            columnCount = 5;
            rowCount = 3;
        }
    </script>
</body>
</html>
